<?php

use App\Resources\LeadResource;
use App\Resources\ProjectResource;
use App\Resources\ProjectTypeResource;
use App\Services\CompanySettingService;
use Common\Models\ProjectType;
use Common\Models\ProjectTypeLegacy;
use Core\Components\Http\StaticAccessors\URI;
use Core\Components\Resource\Classes\Scope;
use Core\StaticAccessors\Config;
	
	$companyProfileDisplay = NULL;
	$accountDisplay = NULL;
	$metricsNavDisplay = NULL;
	$crewManagementNavDisplay = NULL;
	$marketingNavDisplay = NULL;
	$setupDisplay = NULL;
	$customerID = NULL;
	$propertyID = NULL;
	$leadID = NULL;
    $leadUUID = NULL;
	$PHP_SELF = NULL;
	$todaysDateDefault = date('Y-m-d');
	$todaysDateDisplay = date('l - F j, Y');
	$todaysDateMDY = date('n/j/Y');
	$disabledProperty = NULL;
	$disabledProject = NULL;
	$firstName = NULL;
	$lastName = NULL;
	$address = NULL;
	$address2 = NULL;
	$city = NULL;
	$state = NULL;
	$zip = NULL;
	$county = NULL;
	$township = NULL;
	$ownerAddress = NULL;
	$ownerAddress2 = NULL;
	$ownerCity = NULL;
	$ownerState = NULL;
	$ownerZip = NULL;
	$email = NULL;
	$businessName = NULL;
    $assigned_to_user_id  = NULL;
    $marketing_type_id = NULL;
    $project_type_id = null;
    $priority = null;
	$repeatBusinessMarketingTypeID = NULL;
	$latitude = NULL;
	$longitude = NULL;
	$scheduledDateEmail = NULL;
	$scheduledTimeEmail = NULL;
	$companyPhone = NULL;
    $notes = NULL;
    $working_notes = NULL;
    $summary = NULL;
    $unsubscribed = NULL;


	$brandingStylesheet = NULL;
	$brandingFavicon = NULL;

	$projectNameDisplay = null;

	$addPhoneRow = 1;

	$submitButton = '<input class="button" type="submit" id="submitNewCustomer" name="submitNewCustomer" value="Save" />';

	$pageTitle = 'Add Customer';

	$phoneDisplay = NULL;

    $lists = Config::get('lists');

    $buttonDisplay = '
       <div class="row expanded first-row" id="customerButtons">
            <div class="medium-12 columns" style="margin-top: 2rem;">
                <div class="button-group text-center">
                    <a href="'.URI::route('page.app.customers').'" class="button bar left">Existing</a>
                    <a id="addNew" class="button bar right active">New Customer</a>
                </div>
            </div>
        </div>';
	
	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID'];
	}

	if(isset($_GET['cid'])) {
		$customerID = filter_input(INPUT_GET, 'cid', FILTER_SANITIZE_NUMBER_INT);
	} 

	if(isset($_GET['pid'])) {
		$propertyID = filter_input(INPUT_GET, 'pid', FILTER_SANITIZE_NUMBER_INT);
	}

    if(isset($_GET['lid'])) {
        $leadUUID = filter_input(INPUT_GET, 'lid', FILTER_SANITIZE_STRING);
    }

include_once(__DIR__ . '/includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$userFirstName = $userArray['userFirstName'];
		$userLastName = $userArray['userLastName'];
		$userPhoneDirect = $userArray['userPhoneDirect'];
		$userPhoneCell = $userArray['userPhoneCell'];
		$userEmail = $userArray['userEmail'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$marketing = $userArray['marketing'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$calendarBgColor = $userArray['calendarBgColor'];
		$timecardApprover = $userArray['timecardApprover'];

		$companyLatitude = $userArray['latitude'];  //FXLRATR-258
		$companyLongitude = $userArray['longitude']; //FXLRATR-258
		$companyAddress1 = $userArray['address']; //FXLRATR-258
		$companyAddress2 = $userArray['address2']; //FXLRATR-258
		$companyCity = $userArray['city']; //FXLRATR-258
		$companyState = $userArray['state']; //FXLRATR-258
		$companyZip = $userArray['zip']; //FXLRATR-258

        $setting_service = new CompanySettingService((int) $companyID);
        $marketing_source_required = $setting_service->get('marketing_source_required', false);
        $project_type_required = $setting_service->get('project_type_required', false);
        $summary = $setting_service->get('default_project_summary', null);

        $business_hours = $setting_service->get('business_hours_config');
        $formatted_business_hours = [];
        if ($business_hours === null) {
            $formatted_business_hours = [
                [
                    'start' => '8:00',
                    'end' => '17:00',
                    'dow' => [1, 2, 3, 4, 5]
                ]
            ];
        } else {
            foreach ($business_hours as $item) {
                if ($item['status'] === 0) {
                    continue;
                }
                $formatted_business_hours[] = [
                    'start' => $item['start'],
                    'end' => $item['end'],
                    'dow' => [$item['day']]
                ];
            }
        }

	include_once(__DIR__ . '/includes/classes/class_GetMarketingTypes.php');
		$object = new GetMarketingTypes();
		$object->setCompanyID($companyID);
		$object->getRepeatBusinessMarketingTypeID();
		$marketingResults = $object->getResults();
		if (!empty($marketingResults)){
			$repeatBusinessMarketingTypeID = $marketingResults['marketingTypeID'];
		}
		else{
			$repeatBusinessMarketingTypeID = NULL;
		}

   if(!empty($customerID)) {
		include_once(__DIR__ . '/includes/classes/class_Customer.php');
				
			$object = new Customer();
			$object->setCustomer($customerID, $companyID);
			$object->getCustomer();
			$customerArray = $object->getResults();	

			$firstName = $customerArray['firstName'];
			$lastName = $customerArray['lastName'];
			$ownerAddress = $customerArray['ownerAddress'];
			$ownerAddress2 = $customerArray['ownerAddress2'];
			$ownerCity = $customerArray['ownerCity'];
			$ownerState = $customerArray['ownerState'];
			$ownerZip = $customerArray['ownerZip'];
			$email = $customerArray['email'];
			$businessName = $customerArray['businessName'];
            $unsubscribed = $customerArray['unsubscribed'];

		//Phone	
		include_once(__DIR__ . '/includes/classes/class_CustomerPhone.php');
				
			$object = new CustomerPhone();
			$object->setCustomer($customerID);
			$object->getPhone();
			$phoneArray = $object->getResults();	

			$phone_types = [
			    \Common\Models\CustomerPhone::TYPE_CELL => ['Cell', 'Cell (Text Opt-in)'],
                \Common\Models\CustomerPhone::TYPE_CELL_TEXT_OPT_OUT => ['CellNoText', 'Cell (Text Opt-out)'],
                \Common\Models\CustomerPhone::TYPE_HOME => ['Home', 'Home'],
                \Common\Models\CustomerPhone::TYPE_WORK => ['Work', 'Work'],
                \Common\Models\CustomerPhone::TYPE_OTHER => ['Other', 'Other']
            ];
			
			foreach($phoneArray as &$row) {
				$phoneNumber = $row['phoneNumber'];
				$phoneDescription = $row['phoneDescription'];
				$isPrimary = $row['isPrimary'];
				
				if ($isPrimary == '1') {
					$primaryPhone = 'checked';
				} else {
					$primaryPhone = '';
				}
				
				$phoneDisplay .= '
					<tr>
                  		<td class="description" style="color:#36413e;text-align:center;">
                  			<div class="f-field">
                                <select class="f-f-input" disabled name="description">
                                    <option></option>';
                    foreach ($phone_types as $phone_type => [$value, $label]) {
                        $phoneDisplay .= '<option value="' . $value . '"' . ($row['type'] === $phone_type ? ' selected' : '') . '>' . $label . '</option>';
                    }
                    $phoneDisplay .= '
                                </select>
                     		</div>
                    	</td>
                     	<td class="number" style="color:#36413e;">
                     	    <div class="f-field">
                        	    <input disabled class="f-f-input phone" name="phone" type="text" value="'.$phoneNumber.'">
                        	</div>
                       	</td>
                     	<td class="primary" style="color:#36413e;text-align:center;">
                     	    <div class="f-field">
                          	    <input disabled class="f-f-input isPrimary" name="isPrimary" type="checkbox" value="1" '.$primaryPhone.'>
                            </div>
                       	</td>
                     	<td class="delete" style="color:#36413e;text-align:center;"></td>
                 	</tr>
					';	
			}	

			$disabledProperty = 'disabled';
			$pageTitle = 'Add Property';
			$buttonDisplay = '
                <div class="row expanded first-row" id="customerButtons">
                    <div class="medium-12 columns" style="margin-top: 2rem;">
                    </div>
                </div>';
			$submitButton = '<input class="button" type="submit" id="submitNewProperty" name="submitNewProperty" value="Save" />';

			$addPhoneRow = 0;
	}

	if(!empty($propertyID)) {
		include_once(__DIR__ . '/includes/classes/class_Property.php');
			
			$object = new Property();
			$object->setProperty($propertyID, $companyID);
			$object->getProperty();
			$propertyArray = $object->getResults();	
			
			//property
			$customerID = $propertyArray['customerID'];
			$firstName = $propertyArray['firstName'];
			$lastName = $propertyArray['lastName'];
			$ownerAddress = $propertyArray['ownerAddress'];
			$ownerAddress2 = $propertyArray['ownerAddress2'];
			$ownerCity = $propertyArray['ownerCity'];
			$ownerState = $propertyArray['ownerState'];
			$ownerZip = $propertyArray['ownerZip'];
			$address = $propertyArray['address'];
			$address2 = $propertyArray['address2'];
			$city = $propertyArray['city'];
			$state = $propertyArray['state'];
			$zip = $propertyArray['zip'];
			$county = $propertyArray['county'];
			$township = $propertyArray['township'];
			$latitude = $propertyArray['latitude'];
			$longitude = $propertyArray['longitude'];
			$email = $propertyArray['email'];
        	$businessName = $propertyArray['businessName'];

		//Phone	
		include_once(__DIR__ . '/includes/classes/class_CustomerPhone.php');
				
			$object = new CustomerPhone();
			$object->setCustomer($customerID);
			$object->getPhone();
			$phoneArray = $object->getResults();

            $phone_types = [
                \Common\Models\CustomerPhone::TYPE_CELL => ['Cell', 'Cell (Text Opt-in)'],
                \Common\Models\CustomerPhone::TYPE_CELL_TEXT_OPT_OUT => ['CellNoText', 'Cell (Text Opt-out)'],
                \Common\Models\CustomerPhone::TYPE_HOME => ['Home', 'Home'],
                \Common\Models\CustomerPhone::TYPE_WORK => ['Work', 'Work'],
                \Common\Models\CustomerPhone::TYPE_OTHER => ['Other', 'Other']
            ];
			
			foreach($phoneArray as &$row) {
				$phoneNumber = $row['phoneNumber'];
				$phoneDescription = $row['phoneDescription'];
				$isPrimary = $row['isPrimary'];
				
				if ($isPrimary == '1') {
					$primaryPhone = 'checked';
				} else {
					$primaryPhone = '';
				}
				
				$phoneDisplay .= '
					<tr>
                  		<td class="description" style="color:#36413e;text-align:center;">
                  		    <div class="f-field">
                  			    <select class="f-f-input" disabled name="description">
                        		    <option></option>';
                foreach ($phone_types as $phone_type => [$value, $label]) {
                    $phoneDisplay .= '<option value="' . $value . '"' . ($row['type'] === $phone_type ? ' selected' : '') . '>' . $label . '</option>';
                }
				$phoneDisplay .= '
                     		    </select>
                            </div>
                    	</td>
                     	<td class="number" style="color:#36413e;">
                     	    <div class="f-field">
                        	    <input disabled class="f-f-input phone" name="phone" type="text" value="'.$phoneNumber.'">
                            </div>
                       	</td>
                     	<td class="primary" style="color:#36413e;text-align:center;">
                     	    <div class="f-field">
                          	    <input disabled class="f-f-input isPrimary" name="isPrimary" type="checkbox" value="1" '.$primaryPhone.'>
                            </div>
                       	</td>
                     	<td class="delete" style="color:#36413e;text-align:center;"></td>
                 	</tr>
					';	
			}	

			$disabledProject = 'disabled';
			$pageTitle = 'Add Project';
            $buttonDisplay = '
                <div class="row expanded first-row" id="customerButtons">
                    <div class="medium-12 columns" style="margin-top: 2rem;">
                    </div>
                </div>';
			$submitButton = '<input class="button" type="submit" id="submitNewProject" name="submitNewProject" value="Save" />';
			$addPhoneRow = 0;
	}

    if(!empty($leadUUID)) {
        $lead_resource = LeadResource::make(Auth::acl());
        $lead_scope = Scope::make()
            ->filter('lead_uuid', 'eq', $leadUUID);
        $lead_collection = $lead_resource->collection()->scope($lead_scope)->run();
        $lead = $lead_collection[0];

        if ($lead !== null) {
            $leadID = $lead['id'];

            $firstName = $lead['first_name'];
            $lastName = $lead['last_name'];
            $address = $lead['address'];
            $address2 = $lead['address_2'];
            $city = $lead['city'];
            $state = $lead['state'];
            $zip = $lead['zip'];
            $email = $lead['email'];
            $businessName = $lead['business_name'];
            $assigned_to_user_id = $lead['assigned_to_user_id'];
            $marketing_type_id = $lead['marketing_type_id'];
            $project_type_id = str_replace('-', '', strtoupper($lead['project_type_id']));
            $priority = $lead['priority'];
            $notes = $lead['notes'];
            $working_notes = $lead['working_notes'];
            $unsubscribed = $lead['is_unsubscribed'];

            if ($notes) {
                $notes = 'Lead Notes: ' . strip_tags($lead['notes']);
            }
            if ($working_notes) {
                $is_notes = $notes !== null ? "\r\n" : '';
                $working_notes = $is_notes .'Lead Working Notes: ' . strip_tags($lead['working_notes']);
            }

            $phoneNumber = $lead['phone_number'];

            $phone_types = [
                \Common\Models\CustomerPhone::TYPE_CELL => ['Cell', 'Cell (Text Opt-in)'],
                \Common\Models\CustomerPhone::TYPE_CELL_TEXT_OPT_OUT => ['CellNoText', 'Cell (Text Opt-out)'],
                \Common\Models\CustomerPhone::TYPE_HOME => ['Home', 'Home'],
                \Common\Models\CustomerPhone::TYPE_WORK => ['Work', 'Work'],
                \Common\Models\CustomerPhone::TYPE_OTHER => ['Other', 'Other']
            ];

            $phoneDisplay .= '
                        <tr>
                            <td class="description" style="color:#36413e;text-align:center;">
                                <div class="f-field">
                                    <select class="f-f-input" name="description">
                                        <option></option>';
            foreach ($phone_types as $phone_type => [$value, $label]) {
                $phoneDisplay .= '<option value="' . $value . '">' . $label . '</option>';
            }
            $phoneDisplay .= '</select>
                                </div>
                            </td>
                            <td class="number" style="color:#36413e;">
                                <div class="f-field">
                                    <input class="f-f-input phone" name="phone" type="text" value="'.$phoneNumber.'">
                                </div>
                            </td>
                            <td class="primary" style="color:#36413e;text-align:center;">
                                <div class="f-field">
                                    <input checked class="f-f-input isPrimary" name="isPrimary" type="checkbox" value="1" checked>
                                </div>
                            </td>
                            <td class="delete" style="color:#36413e;text-align:center;"></td>
                        </tr>';
            $addPhoneRow = 0;
        }
    }


    $projectTypesLegacy = ProjectTypeLegacy::where('companyID', Auth::user()->companyID)->ordered()->get();

	if (count($projectTypesLegacy) >= 1) {
        $projectNameDisplay = '<span class="projectNameSelect2"><select class="f-f-input" name="projectDescription" multiple="multiple">';
        foreach ($projectTypesLegacy as $projectTypeLegacy) {
            $projectNameDisplay .= '<option value="'.$projectTypeLegacy->type.'">'.$projectTypeLegacy->type.'</option>';
        }
        $projectNameDisplay .= '</select></span>';
    } else {
		$projectNameDisplay = '<input class="f-f-input" name="projectDescription" maxlength="250" type="text" required />';
	}

    $priorities = ProjectResource::getPriorityNames();
    $projectTypesEntity = ProjectType::where('companyID', Auth::user()->companyID)
        ->where('status', '=', ProjectTypeResource::STATUS_ACTIVE)
        ->ordered()
        ->get();

    $projectTypes = [];
    foreach ($projectTypesEntity as $type) {
        $item = [];
        $item['projectTypeID'] = strtoupper(bin2hex($type->projectTypeID));
        $item['type'] = $type->name;
        $projectTypes[] = $item;
}

?>