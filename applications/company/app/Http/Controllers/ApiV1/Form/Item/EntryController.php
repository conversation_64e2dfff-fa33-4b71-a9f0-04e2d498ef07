<?php

namespace App\Http\Controllers\ApiV1\Form\Item;

use App\ResourceMediaHandlers\Form\Item\Entry\Group\FileHandler;
use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Resources\Form\Item\EntryResource;
use App\Traits\Resource\Controller\BaseTrait;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Exception;

class EntryController
{
    use BaseTrait;

    protected $resource = EntryResource::class;

    public function upload($id, RequestInterface $request)
    {
        try {
            $this->getResource()->findOrFail($id, [
                'check_mutability' => true
            ]);

            /** @var FieldFileResource $resource */
            $resource = $this->getResource()->relationResource('groups')
                ->relationResource('field_files');

            /** @var FileHandler $handler */
            $handler = $resource->getMediaHandler('file');
            $file_id = $handler->save(Entity::make([
                'file' => $request->get('file')
            ]));
            return Response::api([
                'file_id' => $file_id
            ]);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }
}
