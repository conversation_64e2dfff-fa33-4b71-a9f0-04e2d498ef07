<?php

declare(strict_types=1);

namespace App\Http\Controllers\ApiV1;

use App\Exceptions\Api\UnprocessableEntityException;
use App\Exceptions\ApiException;
use App\Services\Import\Classes\Ingest;
use App\Services\Import\Classes\Load;
use App\Services\Import\Exceptions\ImportException;
use App\Services\ImportService;
use App\Services\Import\Classes\BaseResource;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validation;
use Core\Components\Validation\Exceptions\ValidationException;
use Core\Interfaces\DBInterface;
use Core\StaticAccessors\App;
use Ramsey\Uuid\Uuid;
use Throwable;

/**
 * Class ImportController
 *
 * @package App\Http\Controllers\App\API
 */
class ImportController
{
    /**
     * Import products from JSON payload
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function importProducts(RequestInterface $request): JSONResponse
    {
        try {
            $user = Auth::user();
            $company_id = $user->companyID;

            $config = FieldConfig::fromArray([
                'products' => [
                    'label' => 'Products',
                    'rules' => 'required|type[array]'
                ]
            ]);

            // Validate the input
            $validation = Validation::make()->config($config);

            $validator = $validation->run($request->input()->post());
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $products = $validator->data('products');

            // Create import service with the database manager
            $db_interface = App::get(DBInterface::class);
            $import_service = new ImportService($db_interface, $company_id);
            $import_id = $import_service->getID();

            // Initialize statistics
            $ingest_stats = [
                'added' => 0,
                'updated' => 0,
                'skipped' => 0,
                'errored' => 0,
                'total' => count($products)
            ];
            
            $load_stats = [
                'inserted' => 0,
                'insert_skipped' => 0,
                'updated' => 0,
                'update_skipped' => 0,
                'errored' => 0,
                'total' => 0
            ];
            $ingestErrorResponse = null;

            // Start transaction
            DB::transaction(function () use ($import_id, $import_service, $products, &$ingest_stats, &$load_stats, &$ingestErrorResponse) {
                // Create ingest instance
                $ingest = new Ingest($import_service);

                try {
                    $timezone = new \DateTimeZone('UTC');
                    $ingest->setTimeZone($timezone);
                } catch (Throwable) {
                    throw new ApiException('Invalid timezone');
                }

                // Process products data
                $this->processProductsData($ingest, $products);

                // Run the ingest process
                $ingest->run();
                
                // Get statistics from the ingest process
                $ingestTotals = $ingest->getTotals();
                $ingest_stats['added'] = $ingestTotals['added'] ?? 0;
                $ingest_stats['updated'] = $ingestTotals['updated'] ?? 0;
                $ingest_stats['skipped'] = $ingestTotals['skipped'] ?? 0;
                $ingest_stats['errored'] = $ingestTotals['errored'] ?? 0;

                // If no errors, run the load process immediately
                if ($ingestTotals['errored'] === 0) {
                    $load = new Load($import_service);
                    $load->run();
                    
                    // Get statistics from the load process
                    $loadTotals = $load->getTotals();
                    $load_stats['inserted'] = $loadTotals['inserted'] ?? 0;
                    $load_stats['insert_skipped'] = $loadTotals['insert_skipped'] ?? 0;
                    $load_stats['updated'] = $loadTotals['updated'] ?? 0;
                    $load_stats['update_skipped'] = $loadTotals['update_skipped'] ?? 0;
                    $load_stats['errored'] = $loadTotals['errored'] ?? 0;
                    $load_stats['total'] = ($loadTotals['inserted'] ?? 0) + 
                                          ($loadTotals['insert_skipped'] ?? 0) + 
                                          ($loadTotals['updated'] ?? 0) + 
                                          ($loadTotals['update_skipped'] ?? 0) + 
                                          ($loadTotals['errored'] ?? 0);
                } else {
                    $ingestErrorResponse = Response::json([
                        'import_id' => $ingest->getID()->toString(),
                        'status' => 'error',
                        'message' => 'Error during data ingestion process',
                        'ingest_stats' => $ingest_stats,
                    ], 422);
                }
            });

            return $ingestErrorResponse ?: Response::json([
                'import_id' => $import_id->toString(),
                'status' => 'success',
                'message' => 'Import process completed',
                'ingest_stats' => $ingest_stats,
                'load_stats' => $load_stats
            ]);

        } catch (Throwable $e) {
            return Response::json([
                'error' => 'An error occurred during import',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process products data and add to ingest
     *
     * @param Ingest $ingest
     * @param array $products
     * @return void
     * @throws ImportException
     */
    private function processProductsData(Ingest $ingest, array $products): void
    {
        // Create a temporary CSV file from JSON data
        $temp_file = tempnam(sys_get_temp_dir(), 'product_import_');
        $csv_file = $temp_file . '.csv';
        rename($temp_file, $csv_file);

        // Get column headers from ProductResource
        $resource_type = BaseResource::TYPE_PRODUCT_ITEM;
        $resource = $ingest->getProcessor($resource_type)->getResource();
        $columns = array_keys($resource->getColumns());

        // Write data to CSV
        $fp = fopen($csv_file, 'w');
        fputcsv($fp, $columns);

        foreach ($products as $product) {
            $row = [];
            foreach ($columns as $column) {
                $row[] = $product[$column] ?? '';
            }
            fputcsv($fp, $row);
        }

        fclose($fp);

        // Add CSV file to ingest
        $ingest->addCsvFile($resource_type, $csv_file);

        // Register cleanup function
        register_shutdown_function(function() use ($csv_file) {
            if (file_exists($csv_file)) {
                unlink($csv_file);
            }
        });
    }
}