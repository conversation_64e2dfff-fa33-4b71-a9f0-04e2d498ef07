<?php

namespace App\ResourceDelegates\Form\Item\Entry\Group;

use App\ResourceMediaHandlers\Form\Item\Entry\Group\File\FieldThumbnailHandler;
use App\ResourceMediaHandlers\Form\Item\Entry\Group\FileHandler;
use App\Resources\Company\Form\Item\FieldResource as CompanyFieldResource;
use App\Resources\FileResource;
use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Resources\Form\Item\Entry\GroupResource;
use App\Resources\Form\Item\Group\FieldResource as GroupFieldResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\FormItemEntryGroupFieldFile;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\MediaList;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class FieldFileDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('group')->resource(GroupResource::class);
        $list->polymorphic('field')
            ->typeField('field_source')
            ->idField('field_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(FieldFileResource::FIELD_SOURCE_GROUP)->resource(GroupFieldResource::class);
                $relation->type(FieldFileResource::FIELD_SOURCE_COMPANY)->resource(CompanyFieldResource::class);
            });
        $list->oneOrMany('file')->resource(FileResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemEntryGroupFieldFileID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([FieldFileResource::ACTION_CREATE, FieldFileResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            })
            ->enableAction(FieldFileResource::ACTION_FILTER);

        $list->field('group_id')
            ->typeUuid()
            ->column('formItemEntryGroupID', true)
            ->validation('Group Id', 'required|uuid|check_group_id')
            ->onAction(FieldFileResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $field_source_map = FieldFileResource::getFieldSourceMap();
        $list->field('field_source')
            ->column('fieldSource', true)
            ->validation('Field Source', 'required|type[int]|in_array[field_sources]')
            ->saveMutator(function ($value) use ($field_source_map) {
                return array_search($value, $field_source_map);
            })
            ->outputMutator(function ($value) use ($field_source_map) {
                return $field_source_map[$value];
            });

        $list->field('field_id')
            ->typeUuid()
            ->column('fieldID', true)
            ->validation('Field Id', 'required|uuid|check_field_id')
            ->onAction(FieldFileResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('file_id')
            ->typeUuid()
            ->column('fileID', true)
            ->validation('File Id', 'required|uuid|check_file_id');

        $this->timestampFields($list);

        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('file')
            ->directoryName('form-files', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(FileHandler::class);
                $type->variant('field_thumbnail')
                    ->directoryName('field-thumbnail', true)
                    ->handler(FieldThumbnailHandler::class);
            });

        return $list;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('field_sources', FieldFileResource::getFieldSources());
        return $config;
    }

    public function validationRules(Rules $rules, FieldFileResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_group_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('group')->entityExists($id->toString())) {
                return true;
            }
            return 'check_group_id';
        }, [
            'check_group_id' => 'Unable to find group'
        ]);

        $rules->register('check_field_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('field_source')) {
                return Rules::STOP;
            }
            $source = $validator->data('field_source');
            $field_resource = $resource->polyRelationResource('field', $source);

            if (!$field_resource->entityExists($id->toString())) {
                return 'check_field_id';
            }
            return true;
        }, [
            'check_field_id' => 'Unable to find field'
        ]);

        $rules->register('check_file_id', function (UuidInterface $id) use ($resource) {
            if (!$resource->relationResource('file')->entityExists($id->toString())) {
                return 'check_file_id';
            }
            return true;
        }, [
            'check_file_id' => 'Unable to find file'
        ]);

        return $rules;
    }

    public function queryScopeGlobal($query, FieldFileResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, FieldFileResource $resource)
    {
        if (($action & FieldFileResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary || $user->projectManagement || $user->sales) {
            return true;
        }
        return false;
    }

    public function modelIsMutable(FormItemEntryGroupFieldFile $model, FieldFileResource $resource)
    {
        return $resource->relationResource('group')->isModelMutable($model->group);
    }

    public function scopeBuildBefore(Scope $scope)
    {
        $format = $scope->getFormat();
        switch ($format) {
            case 'form-v1':
                $scope->fields(['id', 'field_source', 'field_id', 'file_id'], true);
                break;
            case 'detail-v1':
            case 'form-client-v1':
                if ($format === 'detail-v1') {
                    $scope->noFields();
                } elseif ($format === 'form-client-v1') {
                    $scope->fields(['id', 'file_id']);
                }
                $scope->with([
                    'file' => [
                        'fields' => ['name', 'extension', 'size']
                    ],
                    'file_media_urls'
                ]);
                break;
        }
    }
}
