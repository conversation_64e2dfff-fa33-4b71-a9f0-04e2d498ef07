<?php

namespace App\Services;

use App\Resources\ContentPartialResource;
use App\Resources\ContentTemplateResource;
use Core\Classes\Directory;
use Core\Components\Resource\Classes\Scope;
use Core\Exceptions\AppException;
use Core\StaticAccessors\App;
use Core\StaticAccessors\Path;
use Exception;
use LightnCandy\LightnCandy;
use LightnCandy\SafeString;

/**
 * Class ContentTemplateService
 *
 * Class has to be static since the compiled template files cannot work with instances
 *
 * @package App\Services
 *
 * @todo add support for nested template style/script stacking
 */
abstract class ContentTemplateService
{
    protected static $template_resource = null;
    protected static $partial_resource = null;

    public static function clearCache()
    {
        Directory::clear(Path::get('contentTemplateCache'));
    }

    public static function renderPartial($alias, $context)
    {
        try {
            return new SafeString(ContentPartialService::renderByAlias(self::$partial_resource, $alias, $context));
        } catch (Exception $e) {
            error_log($e);
            return '';
        }
    }

    public static function compileTemplate($data)
    {
        $helper_class = ContentTemplate\Helpers::class;
        return LightnCandy::compile($data, [
            'flags' => LightnCandy::FLAG_HANDLEBARS | LightnCandy::FLAG_RUNTIMEPARTIAL | LightnCandy::FLAG_EXTHELPER | LightnCandy::FLAG_ERROR_EXCEPTION,
            'helpers' => [
                'partial' => "{$helper_class}::partial",
                'ifeq' => "{$helper_class}::ifEquals",
                'format-text' => "{$helper_class}::formatText",
                'format-date' => "{$helper_class}::formatDate",
                'format-number' => "{$helper_class}::formatNumber",
                'format-currency' => "{$helper_class}::formatCurrency",
                'chunk' => "{$helper_class}::chunk",
                'google-streetview-image-url' => "{$helper_class}::googleStreetViewImageUrl",
                'modify-date' => "{$helper_class}::modifyDate",
                'math' => "{$helper_class}::math",
                'ifCond' => "{$helper_class}::ifCond"
            ]
        ]);
    }

    public static function getCachePath($id)
    {
        return Path::contentTemplateCache($id);
    }

    public static function render(ContentTemplateResource $template_resource, ContentPartialResource $partial_resource, $id, $context = null)
    {
        self::$template_resource = $template_resource;
        self::$partial_resource = $partial_resource;

        $fields = ['id', 'styles', 'scripts', 'company_id'];

        $cache_path = self::getCachePath($id);
        $cache_hit = !App::debugEnabled() && file_exists($cache_path);

        if (!$cache_hit) {
            $fields[] = 'content';
        }

        $template = $template_resource->entity($id)->scope(Scope::make()->fields($fields))->run();

        if (!$cache_hit) {
            // compile template and save to cache
            $php = self::compileTemplate($template['content']);
            if (file_put_contents($cache_path, '<?php ' . $php . ' ?>') === false) {
                throw new AppException('Unable to save template');
            }
        }

        $renderer = include($cache_path);

        // if bid has zero margins we need to force an addition to the page styles
        $settings = new CompanySettingService($template->company_id);
        $bid_zero_margins = $settings->get('bid_zero_margins', false);
        if ($bid_zero_margins) {
            $template['styles'] = $template['styles'] . '.page {padding: .5in .75in; width: 8.5in; height: 11in;}';
        }

        return [
            'styles' => [$template['styles']],
            'content' => $renderer($context === null ? [] : $context)
        ];
    }

    public static function clearCacheByID($id)
    {
        $path = self::getCachePath($id);
        if (!file_exists($path)) {
            return;
        }
        if (!unlink($path)) {
            throw new AppException('Unable to remove content template cache file: %s', $path);
        }
    }
}
