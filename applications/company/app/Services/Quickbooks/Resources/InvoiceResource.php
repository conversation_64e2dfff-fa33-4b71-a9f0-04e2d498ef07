<?php

declare(strict_types=1);

namespace App\Services\Quickbooks\Resources;

use App\Services\CompanySettingService;
use App\Services\Quickbooks\Classes\Resource;
use App\Services\Quickbooks\Traits\Resource\DownloadPdfTrait;
use Brick\Math\BigDecimal;
use Carbon\Carbon;

/**
 * Class InvoiceResource
 *
 * @package App\Services\Quickbooks\Resources
 * @method \QuickBooksOnline\API\Data\IPPInvoice|null find($id)
 * @method \QuickBooksOnline\API\Data\IPPInvoice findOrFail($id)
 * @method \QuickBooksOnline\API\Data\IPPInvoice[] all(?\Closure $query = null)
 */
class InvoiceResource extends Resource
{
    use DownloadPdfTrait;

    /**
     * @var string entity name
     */
    protected $name = 'Invoice';

    /**
     * Get last used invoice number from QB
     *
     * @return string|null
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \App\Services\Quickbooks\Exceptions\QueryException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function getLastInvoiceNumber(): ?string
    {
        $result = $this->query()->where('DocNumber', '!=', null)
            ->orderBy('Id', 'desc')
            ->limit(1)
            ->first();
        return $result !== null ? $result->DocNumber : null;
    }

    /**
     * Convert lines into proper entity data useful for request payload
     *
     * @param array $lines
     * @return array
     */
    protected function buildLines(array $lines): array
    {
        return array_map(function ($line) {
            return [
                'DetailType' => 'SalesItemLineDetail',
                'Amount' => $line['amount'],
                'Description' => $line['description'],
                'SalesItemLineDetail' => [
                    'ItemRef' => [
                        'value' => $line['item']
                    ],
                    'UnitPrice' => $line['amount'],
                    'Qty' => 1.0
                ]
            ];
        }, $lines);
    }

    /**
     * Create invoice from legacy code
     *
     * @param string $customer_id
     * @param string|null $invoice_number
     * @param array $lines
     * @param int $due_days
     * @return array
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \App\Services\Quickbooks\Exceptions\QuickbooksException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function legacyCreate(string $customer_id, ?string $invoice_number, array $lines, ?string $customer_email, int $due_days = 0): array
    {
        // @todo update to company timezone
        $now = Carbon::now();
        $data = [
            'TxnDate' => $now->toDateString(),
            'DueDate' => $now->addDays($due_days)->toDateString(),
            'CustomerRef' => [
                'value' => $customer_id
            ],
            'Line' => $this->buildLines($lines)
        ];
        if ($customer_email !== null) {
            $data['BillEmail'] = [
                'Address' => $customer_email
            ];
        }
        if ($invoice_number !== null) {
            $data['DocNumber'] = $invoice_number;
        }
        //@todo need to remove allowOnlinePayments in quickbooksOAuth table, now storing this in company settings
        $setting_service = new CompanySettingService($this->service->getCompanyID());
        $data['AllowOnlineCreditCardPayment'] = $setting_service->get('quickbooks_allow_online_credit_card_payment', false);
        $data['AllowOnlineACHPayment'] = $setting_service->get('quickbooks_allow_online_ach_payment', false);
        $invoice = $this->create($data);
        return [
            'invoiceID' => $invoice->Id,
            'invoiceNumber' => $invoice->DocNumber
        ];
    }

    /**
     * Update invoice from legacy code
     *
     * @param string $id
     * @param array $lines
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \App\Services\Quickbooks\Exceptions\ObjectNotFoundException
     * @throws \App\Services\Quickbooks\Exceptions\QuickbooksException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function legacyUpdate(string $id, array $lines): void
    {
        $date = Carbon::now()->toDateString();
        $this->partialUpdate($id, [
            'TxnDate' => $date,
            'DueDate' => $date,
            'Line' => $this->buildLines($lines)
        ]);
    }

    /**
     * Find invoices by list of ids
     *
     * @param array $ids
     * @return array
     * @throws \App\Services\Quickbooks\Exceptions\OAuth\DisconnectedException
     * @throws \App\Services\Quickbooks\Exceptions\QueryException
     * @throws \Core\Exceptions\AppException
     * @throws \QuickBooksOnline\API\Exception\SdkException
     */
    public function legacyFindByIds(array $ids): array
    {
        /** @var \QuickBooksOnline\API\Data\IPPInvoice[] $entities */
        $entities = $this->query()->whereIn('Id', $ids)->run();
        $invoices = [];
        foreach ($entities as $entity) {
            $invoices[] = [
                'invoiceID' => $entity->Id,
                'invoiceNumber' => $entity->DocNumber,
                'balance' => $entity->Balance,
                'paid' => BigDecimal::of($entity->TotalAmt)->isGreaterThan('0') && BigDecimal::of($entity->Balance)->isEqualTo('0') ? 1 : 0
            ];
        }
        return $invoices;
    }
}
