@use '~@cac-sass/base';

.m-additional-costs-list {
    padding: base.unit-rem-calc(12px);
    height: 100%;
    width: 100%;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(12px) 0;
    }
    //  Edit Additional Cost Inside Table
    .m-form {
        display: flex;
        flex-direction: column;
        .c-f-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            row-gap: base.unit-rem-calc(8px);
            column-gap: base.unit-rem-calc(16px);
        }
        .f-field {
            grid-column: span 2;
            &.t-name,
            &.t-unit {
                grid-column: span 3;
                @include base.respond-to('<xsmall') {
                    grid-column: span 6;
                }
            }
            &.t-products {
                grid-column: span 6;
                .c-frf-list {
                    display: flex;
                    flex-wrap: wrap;
                    gap: base.unit-rem-calc(8px);
                    padding-bottom: base.unit-rem-calc(16px);
                    > p {
                        margin-bottom: 0;
                        line-height: 1.5;
                        background-color: base.$color-primary-light-1;
                        color: base.$color-white-default;
                        border-radius: base.unit-rem-calc(24px);
                        padding: base.unit-rem-calc(2px) base.unit-rem-calc(12px);
                    }
                }
            }
            @include base.respond-to('<small') {
                grid-column: span 3;
                &.t-unit-price {
                    grid-column: span 6;
                }
            }
            @include base.respond-to('<xsmall') {
                grid-column: span 6;
            }
        }
    }
}
.c-acl-table {
    height: 100%;
    width: 100%;
    @include base.respond-to('<small') {
        .c-t-table-wrapper {
            border-radius: 0;
            border-width: base.unit-rem-calc(1px) 0;
        }
        .c-t-header {
            padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(12px);
        }
    }
    thead {
        @include base.respond-to('<small') {
            // markup
            th:nth-last-child(4) {
                display: none !important;
            }
            // units
            th:nth-last-child(2) {
                display: none !important;
            }
        }
    }
    tbody {
        @include base.respond-to('<small') {
            // markup
            td:nth-last-child(4) {
                display: none !important;
            }
            // units
            td:nth-last-child(2) {
                display: none !important;
            }
        }
    }
}

// Add Additional Cost Modal
.s-modal {
    .m-form {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(32px);
        padding-bottom: base.unit-rem-calc(16px);
    }
    &.t-add-additional-cost {
        .c-f-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            row-gap: base.unit-rem-calc(8px);
            column-gap: base.unit-rem-calc(16px);
        }
        .f-field {
            grid-column: span 2;
            &.t-name,
            &.t-unit {
                grid-column: span 3;
                @include base.respond-to('<xsmall') {
                    grid-column: span 6;
                }
            }
            @include base.respond-to('<small') {
                grid-column: span 3;
                &.t-unit-price {
                    grid-column: span 6;
                }
            }
            @include base.respond-to('<xsmall') {
                grid-column: span 6;
            }
        }
    }
}
