'use strict';

const Component = require('@ca-package/router/src/component');
const wrapper_tpl = require('@cam-company-profile-tpl/pages/main-components/wrapper.hbs');
const sidemenu_tpl = require('@cam-company-profile-tpl/pages/main-components/sidemenu.hbs');
const sidemenu_item_tpl = require('@cam-company-profile-tpl/pages/main-components/sidemenu_item.hbs');


class SideMenu extends Component {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page} parent
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            active_menu_item: null,
            mobile_menu_open: false,
            menu: [
                {
                    general: {
                        label: 'General Info',
                        route: 'general.details'
                    },
                    settings: {
                        label: 'Company Settings',
                        route: 'settings.email.details'
                    },
                    users: {
                        label: 'Users',
                        route: 'users.items.manager'
                    }
                },
                {
                    products: {
                        label: 'Products',
                        route: 'products.items.manager',
                        submenu_visible: profile_data.features.product_components,
                        submenu: {
                            materials: {
                                label: 'Materials',
                                route: 'materials',
                                visible: profile_data.features.product_components
                            },
                            additional_costs: {
                                label: 'Additional Costs',
                                route: 'additional_costs',
                                visible: profile_data.features.product_components
                            }
                        }
                    },
                    forms: {
                        label: 'Forms',
                        route: 'forms.items.manager'
                    },
                    terms_conditions: {
                        label: 'Terms & Conditions',
                        route: 'terms_conditions.items.manager'
                    }
                },
                {
                    integrations: {
                        label: 'Integrations',
                        route: 'integrations.quickbooks.details'
                    },
                    media: {
                        label: 'Media Library',
                        route: 'media.items.manager',
                        visible: profile_data.features.media_library
                    },
                    emails: {
                        label: 'Emails',
                        route: 'emails.items.manager'
                    },
                    warranties: {
                        label: 'Warranties',
                        route: 'warranties.cover_letter'
                    },
                    bid_customization: {
                        label: 'Bid Customization',
                        route: 'bid_customization.content'
                    }
                }
            ],
            final_menu: {}
        });
    };

    /**
     * Set active item for menu
     *
     * @param {string} active_item
     */
    setActiveItem(active_item) {
        if (this.state.active_menu_item === active_item) {
            return;
        }
        if (this.state.active_menu_item !== null) {
            this.state.final_menu[this.state.active_menu_item].elem.removeClass('t-active');
        }
        let menu_item = this.state.final_menu[active_item];
        menu_item.elem.addClass('t-active');
        this.elem.label.text(menu_item.label);
        this.state.active_menu_item = active_item;
    };

    /**
     * Set active item from route id
     *
     * @param {string} route_id
     */
    setActiveItemFromRoute(route_id) {
        for (let id of Object.keys(this.state.final_menu)) {
            if (route_id.indexOf(id) !== 0) {
                continue;
            }
            this.setActiveItem(id);
            break;
        }
    };

    toggleMobileMenu() {
        if (this.state.mobile_menu_open) {
            this.elem.root.removeClass('t-open');
            this.state.parent.component.addClass('t-closed');
            this.state.mobile_menu_open = false;
            return;
        }
        this.elem.root.addClass('t-open');
        this.state.parent.component.removeClass('t-closed');
        this.state.mobile_menu_open = true;
    };

    /**
     * Handle route change
     *
     * @param {object} data
     */
    onRouteChange(data) {
        this.setActiveItemFromRoute(data.current.name);
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        await super.load(request);
        setTimeout(() => {
            this.elem.icon_arrow.removeClass('t-preload');
            this.elem.icon_lines.removeClass('t-preload');
            this.state.parent.component.removeClass('t-preload');
        }, 300);
        this.setActiveItemFromRoute(this.router.current_route.name);
        this.router.subscribe('route-changed', this, 'onRouteChange');
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request) {
        this.router.unsubscribe('route-changed', this);
        await super.unload(request);
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.menu = this.elem.root.fxFind('menu');
        this.elem.label = this.elem.root.fxFind('label');
        this.elem.mobile_menu = this.elem.root.fxFind('mobile-menu');
        this.elem.icon_arrow = this.elem.root.fxFind('arrow');
        this.elem.icon_lines = this.elem.root.fxFind('lines');

        //Build Menu
        this.state.menu.map((side_bar_items) => {
            let side_menu_group = $(sidemenu_tpl());
            for (let item in side_bar_items) {
                let sub_item = side_bar_items[item];
                this.state.final_menu[item] = sub_item;
                if (sub_item.visible !== undefined && sub_item.visible === false) {
                    continue;
                }

                let side_menu = $(sidemenu_item_tpl({
                    key: item,
                    name: sub_item.label,
                    submenu: sub_item.submenu_visible !== undefined && sub_item.submenu_visible !== false ? sub_item.submenu : false
                }));

                if (sub_item.submenu !== undefined) {
                    let submenu_items = side_menu.fxFind('item');
                    for (let item of submenu_items) {
                        let $item = $(item);
                        sub_item.submenu[$item.data('id')].elem = $item;
                    }

                    for (let submenu in sub_item.submenu) {
                        this.state.final_menu[submenu] = sub_item.submenu[submenu];
                    }
                }

                side_bar_items[item].elem = side_menu;
                side_menu_group.append(side_menu);
            }
            this.elem.menu.append(side_menu_group);
        });

        const that = this;
        this.elem.menu.fxClickWatcher('item', function (e) {
            e.preventDefault();
            let new_route = $(this).data('id');
            if (new_route === that.state.active_menu_item && that.state.final_menu[that.state.active_menu_item].route === that.router.current_route.name) {
                that.toggleMobileMenu();
                return;
            }
            that.toggleMobileMenu();
            that.router.navigate(that.state.final_menu[new_route].route);
        });

        this.elem.mobile_menu.fxClick((e) => {
            e.preventDefault();
            this.toggleMobileMenu();
        });
        this.elem.label.fxClick((e) => {
            e.preventDefault();
            this.toggleMobileMenu();
        });
    };

    /**
     * Render component
     */
    render() {
        return wrapper_tpl();
    };
}

module.exports = SideMenu;
