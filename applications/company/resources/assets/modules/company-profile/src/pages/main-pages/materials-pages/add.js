'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

export class MaterialAdd extends Modal {
    /**
     * Get and cache modal
     *
     * @returns {module:Modal.Base}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../modals/materials/add');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open add modal with promise
     *
     * @returns {Promise<undefined>}
     */
    openModal() {
        return new Promise((resolve, reject) => {
            return this.modal.open({resolve, reject});
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.openModal().then(result => {
            let query = null;
            if (result !== null) {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Material added successfully'));
                query = {update: 'true'};
            }
            this.router.navigate('materials', {}, query);
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.externalClose();
        await super.unload(request, next);
    };
}
