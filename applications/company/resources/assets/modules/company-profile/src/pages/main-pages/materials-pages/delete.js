'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

export class MaterialDelete extends Modal {
    /**
     * Get and cache delete material modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../modals/materials/delete');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open delete material modal with promise
     *
     * @param {string} material_id - uuid
     * @returns {Promise<undefined>}
     */
    openModal(material_id) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                material_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.openModal(request.params.material_id).then((result) => {
            if (result === null) { // no action was taken
                this.router.navigate('materials');
                return;
            }
            this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Material deleted successfully'));
            // redirect instead of navigate to remove the delete from the nav history so user can't hit back
            // and see the same modal which will no longer work
            this.router.redirect('materials');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}
